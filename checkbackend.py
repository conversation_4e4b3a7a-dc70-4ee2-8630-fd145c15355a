import requests
from bs4 import BeautifulSoup
import pandas as pd
from getpass import getpass


print("Nhập tên tài khoản backend:")
username = input()
password = getpass(prompt="Nhập mật khẩu: ")


session = requests.Session()
rq = session.get("https://backend.vtcmobile.vn/AccountInfo.aspx")
soup = BeautifulSoup(rq.text, "html.parser")
try:
    VIEWSTATE = soup.find("input", {"name": "__VIEWSTATE"}).get("value")
    VIEWSTATEGENERATOR = soup.find("input", {"name": "__VIEWSTATEGENERATOR"}).get(
        "value"
    )
    EVENTVALIDATION = soup.find("input", {"name": "__EVENTVALIDATION"}).get("value")
except:
    print("Lỗi")
VIEWSTATE = soup.find("input", {"name": "__VIEWSTATE"}).get("value")
VIEWSTATEGENERATOR = soup.find("input", {"name": "__VIEWSTATEGENERATOR"}).get("value")
EVENTVALIDATION = soup.find("input", {"name": "__EVENTVALIDATION"}).get("value")
urlre = rq.url
pramas = {
    "__VIEWSTATE": VIEWSTATE,
    "__VIEWSTATEGENERATOR": VIEWSTATEGENERATOR,
    "__EVENTVALIDATION": EVENTVALIDATION,
    "TextBox_Email": username,
    "TextBox_Pass": password,
    "Button_Login": "Đăng nhập",
}
rq = session.post(urlre, data=pramas)


file_path = "uid.txt"
lines = []
with open(file_path, "r") as file:
    lines = file.readlines()

datatong = []
for line in lines:
    line = line.strip()

    rq = session.get("https://backend.vtcmobile.vn/AccountInfo.aspx")
    soup = BeautifulSoup(rq.text, "html.parser")
    VIEWSTATE = soup.find("input", {"name": "__VIEWSTATE"}).get("value")
    VIEWSTATEGENERATOR = soup.find("input", {"name": "__VIEWSTATEGENERATOR"}).get(
        "value"
    )
    EVENTVALIDATION = soup.find("input", {"name": "__EVENTVALIDATION"}).get("value")
    pramas = {
        "__VIEWSTATE": VIEWSTATE,
        "__VIEWSTATEGENERATOR": VIEWSTATEGENERATOR,
        "__EVENTVALIDATION": EVENTVALIDATION,
        "ctl00$ContentPlaceHolder1$hdServiceOutput": 200001,
        "ctl00$ContentPlaceHolder1$dlOption": 3,
        "ctl00$ContentPlaceHolder1$txtKeyword": line,
        "ctl00$ContentPlaceHolder1$btOk": "Tìm kiếm",
    }

    rq = session.post("https://backend.vtcmobile.vn/AccountInfo.aspx", data=pramas)
    soup = BeautifulSoup(rq.text, "html.parser")

    if "không tồn tại" in rq.text:
        print("Tài khoản không tồn tại")
    else:

        table = soup.find_all("table", {"class": "df_table"})
        # print(table[3])
        datas = table[3].find_all("tr")

        for data in datas:
            if "330503" in data.get_text():
                print(data.find_all("td")[5].getText())
                datatong.append(
                    {
                        "ID": int(line),
                        "Tên TK": data.find_all("td")[1].getText(),
                        "Thời gian tham gia": data.find_all("td")[5].getText(),
                    }
                )
df = pd.DataFrame(datatong)

# Xuất ra file Excel
df.to_excel("data.xlsx", index=False)
