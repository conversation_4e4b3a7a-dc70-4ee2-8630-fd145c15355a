import requests
import pandas as pd
from bs4 import BeautifulSoup
import os


name = []


def getname(page):
    dataPhim = []
    url = f"https://cms.netreels.net/admin/movies?page={page}"
    payload = {}
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "vi,en-US;q=0.9,en;q=0.8",
        "priority": "u=0, i",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "same-origin",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Cookie": "LANGUAGE_DEFAULT=vi-VN; SESSION_LOGIN_TOKEN=eyJpdiI6ImZ6YTNBam1PQUNzbm5QMXh5Q1FPc3c9PSIsInZhbHVlIjoiSTdNWlAxVXF3bHV2MlNxRVM0a2IreUJHRUNlaU8vYmkwektlcENoU2dtSUVJN01qcXFUSHFlOXdidHNNUG0xT0JXUGE1UllJZWQ1UjJCUlNoV083UjFsckVrcXo4U3A0enRleFp4ZHpKMkdHSGJvcVA3RkwwNFg2TkNMNGhxTlhpc0pnc0Q2UjZTZDhKKy8xQzBHZTRvSlp0d1g3QmRsQ0Qybnl4eGlwTnVja2FBbXk0cXE5NExVSE4ya3dKK2dOV1F0WWhvTmFEQURIYXFBeTF5dThPSVJ6V0NzeDdSVW9sVUpnbVFmVDY1WHJybUdRcFQwTGY4UTgxUCtPaTkwQU9GWXRucVJGQlpIYy9LSGRBek9EOHNkV0MyS0pWTzkwZnRjQ0l2cy9qdkdKbTVpVmNZOC8zRTRzTGVaTU9aWmxUdnhMMVUvZDFWSDBhaGYwTDhPODg1RTRLeUJtNjFmdDU5QjlJWVlkOU1jM1ZCUy9Lb1pyalZQN0xENDY4VWEvNmZPZVV0ZGorRmYrQi9uWU5CZm82UT09IiwibWFjIjoiZmNjOWJhOGQxNjQ5MmU1YjRjNzQyNTYwOTBlZDE2OTQ1ZjdiMDgwZTM1NjZlZDRiMDAwMmY5Zjg5NDA3YTAyMiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InQ3OGVuUEdGSzBYSWxmWlpYZUdzVUE9PSIsInZhbHVlIjoiUzl1NFNJMGRsaUpRZXMrOGpwTUJybVErSjd0aWVLMkM4ZGF1MER2NG1MR2VKTzB1dVAvMVhOVTVKSG9PR1VCQ044cmJlR2tiY1Nxd1dTSnB3YkVTTi9DQ0dmaWM4UzNGMnViQW00U3B2b2MxKy9wdUpYTXowYUZTNjg5UkYrRksiLCJtYWMiOiJmZDFkYzdmNjMyYTBhYWNiYzg5MDNiZjFhYmU0M2MwZjBkNGUxMjI5ODVmNGRjNTM3YWYyODZjZDJhNWQ2YzAwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRYaTZJTEVaUCtDRTgzcEwveU91bEE9PSIsInZhbHVlIjoiNUEwVTZ2bWZpQ3poK3BqeUo0TkdIbEtYTmlCOUJkL3JEYVd0SzlBeHYwWnBOUkRyb0hCWFJPOGxlQlRmVjQ2RkpYQm1nbHpEOElTZG40OGIxTnV5UjFNY2VIdGV4KzRKbkF6RFpGcDVqNTdpYWptV0NXYlFrR1ZJdWhnSHAyYVYiLCJtYWMiOiI5OGEzNDI3NDBkODYyOTQ3Nzc2Y2M4MzNkOWQyYzg1NjIyM2NjMTUzNWYxOWZhZTkwMzFkNzNkMzE2ZmRlYjFiIiwidGFnIjoiIn0%3D",
    }
    response = requests.request("GET", url, headers=headers, data=payload)
    soup = BeautifulSoup(response.text, "html.parser")
    cardBody = soup.find("div", {"class": "card-body"})
    cols = cardBody.find_all("div", {"class": "col-6 col-md-3 col-xl-2 col-xxl-1 mb-6"})

    url = cols[0].find("a").get("href")
    response = requests.request("GET", url, headers=headers, data=payload)
    soup2 = BeautifulSoup(response.text, "html.parser")
    tenPhim = soup2.find("div", {"class": "card-title"})

    dataPhim.append(tenPhim.find("h2").text)
    label = soup2.find_all("div", {"class": "fv-row mb-7"})
    listLabel1 = label[3].find("div", {"class": "list-option-detail"})
    listLabel2 = listLabel1.find_all("li")
    phude = []
    for i in listLabel2:
        phude.append(i.text.strip())
    dataPhim.append(",".join(phude))
    name.append(dataPhim)

    # for col in cols:
    #     url = col.find("a").get("href")
    #     response = requests.request("GET", url, headers=headers, data=payload)
    #     soup2 = BeautifulSoup(response.text, "html.parser")
    #     tenPhim = soup2.find("div", {"class": "card-title"})
    #     print(tenPhim.find("h2").text)
    #     label = soup.find("div", {"class": "fv-row mb-7"})
    #     print(label)


getname(1)
# for i in range(1, 65):
#     print(f"Page {i}")
#     getname(i)

df = pd.DataFrame(name)
df.to_excel("name.xlsx", index=False)
