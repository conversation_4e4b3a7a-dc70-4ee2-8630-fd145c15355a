import re
import pandas as pd


file_path = "sys_2206.log"
lines = []
with open(file_path, "r") as file:
    log_lines = file.readlines()


# Regex patterns
pattern_type = r"\[(\w+)\]"
pattern_pairs = r"(\w+)\[([^\[\]]+)\]"

# <PERSON>h sách chứa dữ liệu dạng dict
data = []

for line in log_lines:
    match_type = re.search(pattern_type, line)
    log_type = match_type.group(1) if match_type else None

    # Tìm tất cả các cặp key-value
    pairs = dict(re.findall(pattern_pairs, line))
    pairs["TYPE"] = log_type  # Thêm TYPE vào dict

    data.append(pairs)

# Tạo DataFrame từ danh sách dict
df = pd.DataFrame(data)

# Ghi vào file Excel
output_file = "log_output.xlsx"
df.to_excel(output_file, index=False)

print(f"Đã ghi dữ liệu vào file: {output_file}")
