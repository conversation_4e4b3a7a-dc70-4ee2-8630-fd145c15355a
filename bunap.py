import hashlib
import urllib.parse
import requests
import random
from datetime import datetime


def generate_url(
    endpoint: str,
    client_id: str,
    game_name: str,
    trans_id: str,
    trans_date: str,
    payment_method: str,
    user_id: str,
    user_name: str,
    service_id: str,
    amount: str,
) -> str:
    # Create the data string to sign
    data = f"{payment_method}{user_id}{user_name}{amount}{service_id}"

    # Create MD5 signature
    signature_string = client_id + data + trans_id + trans_date
    signature = hashlib.md5(signature_string.encode("utf-8")).hexdigest()

    # Build the URL with query parameters
    params = {
        "ClientId": client_id,
        "GameName": game_name,
        "TransId": trans_id,
        "TransDate": trans_date,
        "PaymentMethod": payment_method,
        "UserId": user_id,
        "UserName": user_name,
        "ServiceId": service_id,
        "Amount": amount,
        "Sign": signature,
    }

    # Encode query string
    query_string = urllib.parse.urlencode(params)

    # Final URL
    url = f"{endpoint}/api/v1/topup/get-scoin-delivery-async?{query_string}"

    return url


def checkDate():
    date_str = "Tue, 29 Jul 2025 16:50:48 +0700"
    # Define the date format to match the string exactly, including the day of week (%a) and UTC offset (%z)
    date_format = "%a, %d %b %Y %H:%M:%S %z"
    # Parse the string into a datetime object
    date_obj = datetime.strptime(date_str, date_format)
    # Format datetime object to the desired format dd-mm-yyyy
    formatted_date = date_obj.strftime("%d-%m-%Y")
    print(formatted_date)  # Output: 29-07-2025


# file_path = "log.txt"
# lines = []
# with open(file_path, "r") as file:
#     lines = file.readlines()


# for line in lines:
#     print(line.strip())
#     line = line.strip().split("|")
#     url = generate_url(
#         endpoint="https://api-gf.splay.vn/scoingw",
#         client_id="VTCMobile",
#         game_name="Football",
#         trans_id=line[2],
#         trans_date="20250728085021",
#         payment_method="SCOIN",
#         user_id=line[3],
#         user_name=line[0],
#         service_id="330503",
#         amount=line[1],
#     )
#     # print(url)
#     response = requests.get(url)
#     print(response.json())


# print("Nhập user_id: ")
# user_id = input()
# print("Nhập amount: ")
# amount = input()

trans_id = random.randint(10000000, 99999999)
user_id = "1587506326"
user_name = "gg_115896754769644309203"
amount = "10000000"


url = generate_url(
    endpoint="https://api-gf.splay.vn/scoingw",
    client_id="VTCMobile",
    game_name="MaQuan",
    trans_id=str(trans_id),
    trans_date="20250728085021",
    payment_method="SCOIN",
    user_id=user_id,
    user_name=user_name,
    service_id="330557",
    amount=amount,
)
# print(url)
response = requests.get(url)
print(response.json())
