import requests
import pandas as pd
from bs4 import BeautifulSoup
import os
import re


name = []
folder_name = "img"

# T<PERSON><PERSON> thư mục nếu chưa tồn tại
os.makedirs(folder_name, exist_ok=True)


def getname(page):
    url = f"https://cms.netreels.net/admin/movies?page={page}"
    payload = {}
    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "vi,en-US;q=0.9,en;q=0.8",
        "priority": "u=0, i",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "same-origin",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Cookie": "LANGUAGE_DEFAULT=vi-VN; SESSION_LOGIN_TOKEN=eyJpdiI6ImZ6YTNBam1PQUNzbm5QMXh5Q1FPc3c9PSIsInZhbHVlIjoiSTdNWlAxVXF3bHV2MlNxRVM0a2IreUJHRUNlaU8vYmkwektlcENoU2dtSUVJN01qcXFUSHFlOXdidHNNUG0xT0JXUGE1UllJZWQ1UjJCUlNoV083UjFsckVrcXo4U3A0enRleFp4ZHpKMkdHSGJvcVA3RkwwNFg2TkNMNGhxTlhpc0pnc0Q2UjZTZDhKKy8xQzBHZTRvSlp0d1g3QmRsQ0Qybnl4eGlwTnVja2FBbXk0cXE5NExVSE4ya3dKK2dOV1F0WWhvTmFEQURIYXFBeTF5dThPSVJ6V0NzeDdSVW9sVUpnbVFmVDY1WHJybUdRcFQwTGY4UTgxUCtPaTkwQU9GWXRucVJGQlpIYy9LSGRBek9EOHNkV0MyS0pWTzkwZnRjQ0l2cy9qdkdKbTVpVmNZOC8zRTRzTGVaTU9aWmxUdnhMMVUvZDFWSDBhaGYwTDhPODg1RTRLeUJtNjFmdDU5QjlJWVlkOU1jM1ZCUy9Lb1pyalZQN0xENDY4VWEvNmZPZVV0ZGorRmYrQi9uWU5CZm82UT09IiwibWFjIjoiZmNjOWJhOGQxNjQ5MmU1YjRjNzQyNTYwOTBlZDE2OTQ1ZjdiMDgwZTM1NjZlZDRiMDAwMmY5Zjg5NDA3YTAyMiIsInRhZyI6IiJ9; XSRF-TOKEN=eyJpdiI6InNDcjlYQnM3SHdjQnBSWTIyUW1OZHc9PSIsInZhbHVlIjoiTWFPL3lYUndOSk1kaXFkZ2YzVWk5Vk0vU1g2Sk9FVC9QbEZ2bk9tWHk2c2F1MXhJelkxSTY1MitDTHRMcUtiVlFzN1dwVllIWlIyRGpzQjg2N0ViM3pxT2M2UEh6UXpZOU9QbWoydENHTk1KZ1ZCdzFqRXI0WlpRR3FiVjh4TG4iLCJtYWMiOiI2ZGZmNmRiNjNlNWQ5MTNlNTBmMzAzMjZhNzZmN2JmNjMxMTlkMGE3Nzc5ZDFjZGZkMGNhYmUyZDU3NGZjZTkwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkExNTd0bFRhc1JlT291cjJ5QVc2T2c9PSIsInZhbHVlIjoidlYrSktSbTBtOTlOT0o4Z3M5LzFqajdYVDJHTm82dE5oSXFyWVk4YURjMUZwUVNHVE1WeFEzcC9qMjlxWGNaZ3RCd2dNaDMrVmFqaWNLMFVpNm5hNHYwVTRJOHlzMUZKUVBWbVZzeWdKLzhZc2xNMnRJVG9sU1NRdE1yTXVZcHAiLCJtYWMiOiJlNGY0OTVkYzZiYWNlNmM4OTNjMWZkYTg5OTk3YWY0ZjhlYjllYmJkYzlkODFjMDNiMTBmOTg2OTNmNDA0NTEyIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZxR3NVbjBVWGxHdW9Ta1RUOGNzb3c9PSIsInZhbHVlIjoiWFRJaDBFNWhkRk9VcTBuSXJuLzYvNy9LSmI1aTRaUTIyWHZaYVpsSjBKQ0ltQVVucWYrbEtIUDRNU0M5NlJUcWp3RzhvQ3I1d2kvR3dtOWdYRWY4ZjZGY3JOYVZnVDlwUVV5bUlHWDZYbTR6V0xBL1FJeDNhNjM3SWFwTWNtSVkiLCJtYWMiOiJmYTNjOGI2OWQxZmFhNmFmNWEyMjQ4YTg3MDBhODFkNTkyM2MyMjhiY2M0OTJlOGJlMWQwMmE2MjUxZWI3OWQ5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imx3eWM0MVJzZWhNV0xIVDRuS05Ib3c9PSIsInZhbHVlIjoiRXUyM2tkdnZsNGNBSGZoRnVORDcwQk84dnltV1Y0dEtVNHo3dUo5dTlHV1R6elJ2TDVCQW9GbEt3S09BK3o1K2UrQjdZdHF4VGF4MFNybXZhUEFRY1NWekZGbWxlRWFrT0hWWDJjSWRhTjduaUgxZTgyQnc4MlgzNDdzNUQ2WjYiLCJtYWMiOiJlYTA2ODNmZjQwNGUzOWQwYjIwZDNhYzdiNThlZmU4ZDY1MjhjMGQ2NjUzNDFmMmE5ZTY3MGQxYWZlYjFiYTI5IiwidGFnIjoiIn0%3D",
    }
    response = requests.request("GET", url, headers=headers, data=payload)
    soup = BeautifulSoup(response.text, "html.parser")
    cardBody = soup.find("div", {"class": "card-body"})
    cols = cardBody.find_all("div", {"class": "col-6 col-md-3 col-xl-2 col-xxl-1 mb-6"})
    os.makedirs(os.path.join(folder_name, str(page)), exist_ok=True)
    for col in cols:
        div = col.find(
            "div",
            {
                "class": "h-200px h-md-225px h-xl-175px h-xxl-150px position-relative lazy-bg"
            },
        )
        namePhim = col.find("h3")
        print(namePhim.text)

        url = div.get("data-bg").replace(
            "?image_process=quality,85/resize,w_200/format,webp", ""
        )
        print(url)

        response = requests.get(url)
        file_path = os.path.join(
            folder_name,
            str(page),
            re.sub(r"[^\w\d\u00C0-\u1EF9]+", "-", namePhim.text, flags=re.UNICODE)
            + ".jpg",
        )
        with open(file_path, "wb") as f:
            f.write(response.content)


# getname(1)
for i in range(1, 65):
    print(f"Page {i}")
    getname(i)

# df = pd.DataFrame(name)
# df.to_excel("name.xlsx", index=False)
